"use client";

import UnifiedMunicipalityChart from "../../../components/unified-municipality-chart";
import NewsBox from "../../../components/news-box";
import PredictionChart from "../../../components/prediction-chart";
import { useState, useEffect } from "react";

export default function Body() {
  const [currentTime, setCurrentTime] = useState(new Date());

  // real-time clock ni para sa dashboard - para professional kaayo tan-awon
  // nag update every second para live jud ang time
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date()); // update ang time kada second
    }, 1000);

    // cleanup ang timer para dili mag memory leak - importante ni!
    return () => clearInterval(timer);
  }, []);

  // format ang time para nindot tan-awon - 12:34:56 format
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit' // with seconds para live jud
    });
  };

  // format ang date para readable - "Monday, January 1, 2025" format
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long', // Monday, Tuesday, etc.
      year: 'numeric',
      month: 'long', // January, February, etc.
      day: 'numeric'
    });
  };

  return (
    <div className="flex-1 overflow-auto">
      {/* Header section - nindot kaayo ni nga header with live time */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-[#143D60] mb-2">Dashboard Overview</h1>
            <p className="text-gray-600">Welcome back! Here&apos;s what&apos;s happening with your health data.</p>
          </div>
          {/* live time display sa right side - para professional */}
          <div className="text-right">
            <div className="text-2xl font-bold text-[#143D60]">{formatTime(currentTime)}</div>
            <div className="text-sm text-gray-600">{formatDate(currentTime)}</div>
          </div>
        </div>
      </div>

      {/* Main Content area - diri ang tanan nga charts ug data */}
      <div className="p-6 space-y-6">
        {/* Stats Cards - gihimo nako static lang kay wala pa'y real data from backend */}
        {/* pero nindot man ang design, murag professional dashboard jud */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Total Cases card - first card sa stats */}
          <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 uppercase tracking-wide">Total Cases</p>
                <p className="text-3xl font-bold text-[#143D60] mt-2">1,234</p> {/* static number lang ni for now */}
                <p className="text-sm text-green-600 mt-1">↗ +12% from last month</p> {/* fake percentage pero nindot tan-awon */}
              </div>
              {/* icon sa right side - chart icon para relevant */}
              <div className="w-12 h-12 bg-gradient-to-r from-[#A0C878] to-[#DDEB9D] rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-[#143D60]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
          </div>

          {/* Active Diseases card - second card */}
          <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 uppercase tracking-wide">Active Diseases</p>
                <p className="text-3xl font-bold text-[#143D60] mt-2">8</p> {/* 8 diseases ang gi monitor nato */}
                <p className="text-sm text-yellow-600 mt-1">→ No change</p> {/* stable ra ang count */}
              </div>
              {/* orange icon para standout - plus icon kay diseases */}
              <div className="w-12 h-12 bg-gradient-to-r from-[#EB5B00] to-[#ff6b1a] rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>

          {/* Municipalities card - third ug last card */}
          <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 uppercase tracking-wide">Municipalities</p>
                <p className="text-3xl font-bold text-[#143D60] mt-2">3</p> {/* 3 ra jud ang municipalities nato */}
                <p className="text-sm text-blue-600 mt-1">Lilo-an, Mandaue, Consolacion</p> {/* list sa municipalities */}
              </div>
              {/* location icon - perfect para sa municipalities */}
              <div className="w-12 h-12 bg-gradient-to-r from-[#143D60] to-[#1e4a6b] rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Charts Section - diri ang main charts */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
          {/* Prediction Chart - naa ni ang AI analysis button */}
          <div className="bg-white rounded-2xl shadow-lg p-2 border border-gray-100">
            <PredictionChart /> {/* diri ang AI magic mahitabo */}
          </div>
          {/* News Box - para sa updates ug announcements */}
          <div className="bg-white rounded-2xl shadow-lg p-2 border border-gray-100">
            <NewsBox /> {/* news feed para updated ang users */}
          </div>
        </div>

        {/* Municipality Charts - breakdown per municipality */}
        <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-[#143D60] mb-2">Disease Distribution by Municipality</h2>
            <p className="text-gray-600">Real-time disease case distribution across different municipalities</p>
          </div>
          {/* 3 charts side by side - kada municipality naa'y chart */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <UnifiedMunicipalityChart
              municipalityName="mandaue"
              displayName="Mandaue City"
            />
            <UnifiedMunicipalityChart
              municipalityName="consolacion"
              displayName="Consolacion"
            />
            <UnifiedMunicipalityChart
              municipalityName="lilo-an"
              displayName="Lilo-an"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
