"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getAuth } from 'firebase/auth';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/firebase';

// Oy, color mapping para sa mga diseases - basin ma confuse ang chart ug same color tanan
export const DISEASE_COLORS = {
  tuberculosis: '#8B5CF6',     // Purple
  measles: '#EF4444',          // Red
  malaria: '#F59E0B',          // Amber
  leptospirosis: '#10B981',    // Emerald
  'hiv/aids': '#EC4899',       // Pink
  dengue: '#3B82F6',           // Blue
  cholera: '#06B6D4',          // Cyan
  'syndrome (amses)': '#84CC16', // Lime
  encephalities: '#F97316',    // Orange
  'acute menigitis': '#6366F1', // Indigo
  covid: '#DC2626'             // Red-600
};

export interface DiseaseData {
  Municipality: string;
  DiseaseName: string;
  CaseCount: string;
  Date?: string;
}

export interface ProcessedDiseaseData {
  [diseaseName: string]: {
    totalCases: number;
    municipalities: { [municipality: string]: number };
    color: string;
  };
}

interface DiseaseDataContextType {
  rawData: DiseaseData[];
  processedData: ProcessedDiseaseData;
  loading: boolean;
  refreshData: () => Promise<void>;
}

const DiseaseDataContext = createContext<DiseaseDataContextType | undefined>(undefined);

export const useDiseaseData = () => {
  const context = useContext(DiseaseDataContext);
  if (!context) {
    throw new Error('useDiseaseData must be used within a DiseaseDataProvider');
  }
  return context;
};

interface DiseaseDataProviderProps {
  children: ReactNode;
}

export const DiseaseDataProvider: React.FC<DiseaseDataProviderProps> = ({ children }) => {
  const [rawData, setRawData] = useState<DiseaseData[]>([]);
  const [processedData, setProcessedData] = useState<ProcessedDiseaseData>({});
  const [loading, setLoading] = useState(true);

  const processRawData = (data: DiseaseData[]): ProcessedDiseaseData => {
    const processed: ProcessedDiseaseData = {};

    // Agi ra ni, process ang data para ma organize - medyo messy ang CSV data gud
    data.forEach((item) => {
      const diseaseName = item.DiseaseName?.toLowerCase().trim();
      const municipality = item.Municipality?.trim();
      const caseCount = parseInt(item.CaseCount, 10);

      // Check lang if valid ang data, basin naa'y empty cells sa CSV
      if (diseaseName && municipality && !isNaN(caseCount) && caseCount > 0) {
        // First time makita ni nga disease? Create new entry
        if (!processed[diseaseName]) {
          processed[diseaseName] = {
            totalCases: 0,
            municipalities: {},
            // Kuha ang color from mapping, if wala default gray nalang
            color: DISEASE_COLORS[diseaseName as keyof typeof DISEASE_COLORS] || '#6B7280'
          };
        }

        // Add sa total count ug per municipality count
        processed[diseaseName].totalCases += caseCount;
        processed[diseaseName].municipalities[municipality] =
          (processed[diseaseName].municipalities[municipality] || 0) + caseCount;
      }
    });

    return processed;
  };

  const fetchData = async () => {
    setLoading(true);

    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      console.log('No user authenticated');
      setRawData([]);
      setProcessedData({});
      setLoading(false);
      return;
    }

    try {
      // 🌍 Fetch from centralized collection that contains ALL municipality data
      // This allows all users to see data from all municipalities
      const centralizedCasesRef = collection(
        db,
        'healthradarDB',
        'centralizedData',
        'allCases'
      );

      const snapshot = await getDocs(centralizedCasesRef);
      const data = snapshot.docs.map((doc) => doc.data() as DiseaseData);

      console.log('Fetched centralized disease data:', data);

      setRawData(data);
      setProcessedData(processRawData(data));
    } catch (error) {
      console.error('Error fetching centralized disease data:', error);

      // 🔄 Fallback: Try to fetch from user's own data if centralized data fails
      try {
        console.log('Falling back to user-specific data...');

        const userCasesRef = collection(
          db,
          'healthradarDB',
          'users',
          'healthworker',
          user.uid,
          'UploadedCases'
        );

        const userSnapshot = await getDocs(userCasesRef);
        const userData = userSnapshot.docs.map((doc) => doc.data() as DiseaseData);

        console.log('Fetched user-specific disease data:', userData);

        setRawData(userData);
        setProcessedData(processRawData(userData));
      } catch (fallbackError) {
        console.error('Error fetching user-specific disease data:', fallbackError);
        setRawData([]);
        setProcessedData({});
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const value: DiseaseDataContextType = {
    rawData,
    processedData,
    loading,
    refreshData: fetchData
  };

  return (
    <DiseaseDataContext.Provider value={value}>
      {children}
    </DiseaseDataContext.Provider>
  );
};
