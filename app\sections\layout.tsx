import SideNavbar from "../components/side-navbar";
import { DiseaseDataProvider } from "../contexts/DiseaseDataContext";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <DiseaseDataProvider>
      <div className="flex min-h-screen bg-gradient-to-br from-[#DDEB9D] to-[#A0C878]">
        <SideNavbar />
        {children}
      </div>
    </DiseaseDataProvider>
  );
}
